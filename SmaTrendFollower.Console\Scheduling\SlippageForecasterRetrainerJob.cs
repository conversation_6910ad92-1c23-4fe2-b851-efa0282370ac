using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;
using SmaTrendFollower.MachineLearning.ModelTraining;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Diagnostics;

namespace SmaTrendFollower.Scheduling;

/// <summary>
/// Quartz job for automated weekly slippage forecaster model retraining.
/// Scheduled to run on Sunday at 6:55 PM ET (after SignalRanker and PositionSizer retraining).
/// </summary>
[DisallowConcurrentExecution]
public class SlippageForecasterRetrainerJob : IJob
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SlippageForecasterRetrainerJob> _logger;

    public SlippageForecasterRetrainerJob(
        IServiceProvider serviceProvider,
        ILogger<SlippageForecasterRetrainerJob> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Starting weekly slippage forecaster retraining job at {Time:yyyy-MM-dd HH:mm:ss} UTC", 
            DateTime.UtcNow);

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var trainer = scope.ServiceProvider.GetRequiredService<SlippageForecasterTrainer>();
            var redisService = scope.ServiceProvider.GetService<OptimizedRedisConnectionService>();

            // Step 1: Train new slippage model
            _logger.LogInformation("Training new slippage forecaster model...");
            var success = await trainer.TrainModelAsync();

            if (!success)
            {
                _logger.LogError("Slippage forecaster training failed - aborting retraining job");
                return;
            }

            // Step 2: Update Redis model version for hot-reload
            if (redisService != null)
            {
                await UpdateModelVersionAsync(redisService);
            }

            // Step 3: Clean up old model backups
            await CleanupOldBackupsAsync();

            stopwatch.Stop();
            _logger.LogInformation("Slippage forecaster retraining completed successfully in {Duration:F2} seconds", 
                stopwatch.Elapsed.TotalSeconds);

            // Record metrics
            SmaTrendFollower.Monitoring.MetricsRegistry.MLRetrainTotal
                .WithLabels("slippage_forecaster", "success")
                .Inc();
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error during slippage forecaster retraining job (duration: {Duration:F2}s)", 
                stopwatch.Elapsed.TotalSeconds);

            // Record failure metrics
            SmaTrendFollower.Monitoring.MetricsRegistry.MLRetrainTotal
                .WithLabels("slippage_forecaster", "failure")
                .Inc();

            throw; // Re-throw to mark job as failed
        }
    }

    private async Task UpdateModelVersionAsync(OptimizedRedisConnectionService redisService)
    {
        try
        {
            var database = await redisService.GetDatabaseAsync();
            var newVersion = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            
            await database.StringSetAsync("model:slip:version", newVersion);
            
            _logger.LogInformation("Updated slippage model version to {Version} in Redis", newVersion);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update slippage model version in Redis");
            throw;
        }
    }

    private async Task CleanupOldBackupsAsync()
    {
        try
        {
            var modelDirectory = "Model";
            if (!Directory.Exists(modelDirectory))
                return;

            // Find all slippage model backup files
            var backupPattern = "slippage_model_backup_*.zip";
            var backupFiles = Directory.GetFiles(modelDirectory, backupPattern)
                .Select(f => new FileInfo(f))
                .OrderByDescending(f => f.CreationTime)
                .ToList();

            // Keep only the 5 most recent backups
            const int maxBackups = 5;
            var filesToDelete = backupFiles.Skip(maxBackups).ToList();

            foreach (var file in filesToDelete)
            {
                file.Delete();
                _logger.LogDebug("Deleted old slippage model backup: {FileName}", file.Name);
            }

            if (filesToDelete.Any())
            {
                _logger.LogInformation("Cleaned up {Count} old slippage model backup files", filesToDelete.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during slippage model backup cleanup");
        }
    }
}

/// <summary>
/// Extension methods for registering slippage forecaster retraining job
/// </summary>
public static class SlippageForecasterRetrainerJobExtensions
{
    /// <summary>
    /// Adds slippage forecaster retraining job to Quartz scheduler
    /// </summary>
    public static IServiceCollectionQuartzConfigurator AddSlippageForecasterRetrainerJob(
        this IServiceCollectionQuartzConfigurator quartz)
    {
        // Configure the slippage forecaster retrainer job
        var jobKey = new JobKey("SlippageForecasterRetrainer");
        quartz.AddJob<SlippageForecasterRetrainerJob>(opts => opts.WithIdentity(jobKey));

        // Schedule weekly on Sunday at 6:55 PM ET (10:55 PM UTC) - after other ML jobs
        quartz.AddTrigger(t => t
            .ForJob(jobKey)
            .WithIdentity("SlippageForecasterRetrainerTrigger")
            .StartNow()
            .WithCronSchedule("0 55 22 ? * SUN") // Weekly on Sunday at 10:55 PM UTC (6:55 PM ET)
            .WithDescription("Weekly slippage forecaster retraining on Sunday at 6:55 PM ET"));

        return quartz;
    }
}

/// <summary>
/// Configuration options for slippage forecaster retraining job
/// </summary>
public record SlippageForecasterRetrainerConfig(
    int TrainingDataDays = 60,
    int ExperimentTimeSeconds = 300,
    int MaxBackupFiles = 5,
    string CronSchedule = "0 55 22 ? * SUN", // Sunday 10:55 PM UTC (6:55 PM ET)
    bool EnableAutoRetraining = true
);
